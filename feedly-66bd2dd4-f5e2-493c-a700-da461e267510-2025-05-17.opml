<?xml version="1.0" encoding="UTF-8"?>

<opml version="1.0">
    <head>
        <title>Xiao subscriptions in feedly Cloud</title>
    </head>
    <body>
        <outline text="视角" title="视角">
            <outline type="rss" text="The Atlantic" title="The Atlantic" xmlUrl="https://www.theatlantic.com/feed/all/" htmlUrl="https://www.theatlantic.com/"/>
            <outline type="rss" text="Aeon" title="Aeon" xmlUrl="http://feeds.feedburner.com/AeonMagazineEssays" htmlUrl="https://aeon.co"/>
            <outline type="rss" text="The Marginalian" title="The Marginalian" xmlUrl="http://feedproxy.google.com/brainpickings/rss" htmlUrl="https://www.themarginalian.org"/>
            <outline type="rss" text="e-flux conversations - Latest posts" title="e-flux conversations - Latest posts" xmlUrl="http://conversations.e-flux.com/posts.rss" htmlUrl="https://conversations.e-flux.com"/>
            <outline type="rss" text="Nautilus" title="Nautilus" xmlUrl="http://nautil.us/rss/all" htmlUrl="http://nautil.us/rss/all"/>
            <outline type="rss" text="History Today Feed" title="History Today Feed" xmlUrl="http://www.historytoday.com/feed/rss.xml" htmlUrl="https://www.historytoday.com/"/>
            <outline type="rss" text="Artists and Machine Intelligence" title="Artists and Machine Intelligence" xmlUrl="https://medium.com/feed/artists-and-machine-intelligence" htmlUrl="https://medium.com/artists-and-machine-intelligence?source=rss----e1df37a4e9c1---4"/>
            <outline type="rss" text="The Conversation – Articles (US)" title="The Conversation – Articles (US)" xmlUrl="https://theconversation.com/us/articles.atom" htmlUrl="https://theconversation.com"/>
            <outline type="rss" text="视角杂志" title="视角杂志" xmlUrl="http://perspiceremagazine.org/atom.xml" htmlUrl="http://www.chole.io/blog"/>
        </outline>
        <outline text="财经" title="财经">
            <outline type="rss" text="The Economist" title="The Economist" xmlUrl="http://www.economist.com/rss/the_world_this_week_rss.xml" htmlUrl="https://www.economist.com/the-world-this-week"/>
            <outline type="rss" text="China" title="China" xmlUrl="http://www.economist.com/feeds/print-sections/77729/china.xml" htmlUrl="https://www.economist.com/china"/>
            <outline type="rss" text="Science &amp; technology" title="Science &amp; technology" xmlUrl="http://www.economist.com/rss/science_and_technology_rss.xml" htmlUrl="https://www.economist.com/science-and-technology"/>
            <outline type="rss" text="Business" title="Business" xmlUrl="http://www.economist.com/rss/business_rss.xml" htmlUrl="https://www.economist.com/business"/>
            <outline type="rss" text="FTChinese RSS - All Feed" title="FTChinese RSS - All Feed" xmlUrl="http://www.ftchinese.com/sc/rss_r.jsp" htmlUrl="https://www.ftchinese.com/"/>
            <outline type="rss" text="Recent Commits to The" title="Recent Commits to The" xmlUrl="https://github.com/nailperry-zd/The-Economist/commits/master.atom" htmlUrl="https://github.com/nailperry-zd/The-Economist/commits/master"/>
            <outline type="rss" text="财新博客-新世纪的常识传播者-财新网»首页" title="财新博客-新世纪的常识传播者-财新网»首页" xmlUrl="http://blog.caixin.com/feed" htmlUrl="http://blog.caixin.com"/>
            <outline type="rss" text="Harvard Business Review " title="Harvard Business Review " xmlUrl="http://feeds.harvardbusiness.org/harvardbusiness/" htmlUrl="http://hbr.org"/>
            <outline type="rss" text="Finance &amp; economics" title="Finance &amp; economics" xmlUrl="http://www.economist.com/rss/finance_and_economics_rss.xml" htmlUrl="https://www.economist.com/finance-and-economics"/>
            <outline type="rss" text="华尔街见闻" title="华尔街见闻" xmlUrl="https://dedicated.wallstreetcn.com/rss.xml" htmlUrl="https://wallstreetcn.com"/>
        </outline>
        <outline text="区块链" title="区块链">
            <outline type="rss" text="Bitcoin Magazine" title="Bitcoin Magazine" xmlUrl="http://feeds.feedburner.com/BitcoinMagazine" htmlUrl="https://bitcoinmagazine.com"/>
            <outline type="rss" text="Cointelegraph" title="Cointelegraph" xmlUrl="http://cointelegraph.com/rss" htmlUrl="https://cointelegraph.com"/>
            <outline type="rss" text="區塊客" title="區塊客" xmlUrl="http://blockcast.it/feed/" htmlUrl="https://blockcast.it/"/>
            <outline type="rss" text="Bitcoinist" title="Bitcoinist" xmlUrl="http://bitcoinist.net/feed/" htmlUrl="https://bitcoinist.com"/>
            <outline type="rss" text="CoinDesk" title="CoinDesk" xmlUrl="http://feeds.feedburner.com/CoinDesk" htmlUrl="https://www.coindesk.com/"/>
        </outline>
        <outline text="科技" title="科技">
            <outline type="rss" text="Gartner Blog Network" title="Gartner Blog Network" xmlUrl="http://blogs.gartner.com/gbn/feed/" htmlUrl="https://www.gartner.com/en/blogs"/>
            <outline type="rss" text="Science &amp; technology" title="Science &amp; technology" xmlUrl="http://www.economist.com/rss/science_and_technology_rss.xml" htmlUrl="https://www.economist.com/science-and-technology"/>
            <outline type="rss" text="TechMarketView RSS Feeds" title="TechMarketView RSS Feeds" xmlUrl="http://www.techmarketview.com/rss/ukhotviews" htmlUrl="https://www.techmarketview.com"/>
            <outline type="rss" text="MIT Technology Review" title="MIT Technology Review" xmlUrl="http://www.technologyreview.com/rss/rss.aspx" htmlUrl="https://www.technologyreview.com"/>
            <outline type="rss" text="報導者開放實驗室 - Medium" title="報導者開放實驗室 - Medium" xmlUrl="https://medium.com/feed/twreporter" htmlUrl="https://medium.com/twreporter?source=rss----517907b93273---4"/>
            <outline type="rss" text="36氪" title="36氪" xmlUrl="https://36kr.com/feed" htmlUrl="http://36kr.com"/>
            <outline type="rss" text="Engadget Simplified Chinese Summary RSS" title="Engadget Simplified Chinese Summary RSS" xmlUrl="http://cn.engadget.com/rss.xml" htmlUrl="http://cn.engadget.com"/>
            <outline type="rss" text="Gartner Press Releases" title="Gartner Press Releases" xmlUrl="http://www.gartner.com/it/section.jsp?type=press_releases&amp;format=rss" htmlUrl="https://www.gartner.com/en/newsroom"/>
            <outline type="rss" text="Business Latest" title="Business Latest" xmlUrl="http://blog.wired.com/business/atom.xml" htmlUrl="https://www.wired.com"/>
        </outline>
        <outline text="电影" title="电影">
            <outline type="rss" text="电影首发站" title="电影首发站" xmlUrl="http://rsshub.app/dysfz" htmlUrl="http://www.wuhaozhan.net/movie/list/"/>
        </outline>
        <outline text="新闻" title="新闻">
            <outline type="rss" text="纽约时报中文网 国际纵览" title="纽约时报中文网 国际纵览" xmlUrl="http://cn.nytimes.com/rss/news.xml" htmlUrl="https://cn.nytimes.com"/>
            <outline type="rss" text="BBC Chinese" title="BBC Chinese" xmlUrl="http://www.bbc.co.uk/zhongwen/trad/index.xml" htmlUrl="https://www.bbc.com/zhongwen/trad"/>
            <outline type="rss" text="International" title="International" xmlUrl="http://www.economist.com/rss/international_rss.xml" htmlUrl="https://www.economist.com/international"/>
            <outline type="rss" text="BBC" title="BBC" xmlUrl="http://newsrss.bbc.co.uk/rss/newsonline_world_edition/front_page/rss.xml" htmlUrl="https://www.bbc.co.uk/news"/>
            <outline type="rss" text="ChinaFile" title="ChinaFile" xmlUrl="http://www.chinafile.com/feeds.xml" htmlUrl="https://www.chinafile.com/"/>
            <outline type="rss" text="The Atlantic - Business" title="The Atlantic - Business" xmlUrl="http://feeds.feedburner.com/AtlanticBusinessChannel" htmlUrl="https://www.theatlantic.com/business/"/>
            <outline type="rss" text="政见 CNPolitics.org" title="政见 CNPolitics.org" xmlUrl="http://cnpolitics.org/feed/" htmlUrl="http://cnpolitics.org"/>
            <outline type="rss" text="Sixth Tone RSS" title="Sixth Tone RSS" xmlUrl="http://www.sixthtone.com/rss" htmlUrl="https://www.sixthtone.com"/>
            <outline type="rss" text="端傳媒 Initium Media" title="端傳媒 Initium Media" xmlUrl="http://feeds.initium.news/theinitium" htmlUrl="https://theinitium.com/newsfeed/"/>
            <outline type="rss" text="喷嚏网----阅读、发现和分享：8小时外的健康生活！" title="喷嚏网----阅读、发现和分享：8小时外的健康生活！" xmlUrl="http://www.dapenti.com/blog/rss2.asp" htmlUrl="http://www.dapenti.com"/>
            <outline type="rss" text="報導者" title="報導者" xmlUrl="https://www.twreporter.org/a/rss2.xml" htmlUrl="https://www.twreporter.org/"/>
            <outline type="rss" text="Asia" title="Asia" xmlUrl="http://www.economist.com/rss/asia_rss.xml" htmlUrl="https://www.economist.com/asia"/>
            <outline type="rss" text="TIME" title="TIME" xmlUrl="http://rss.time.com/web/time/rss/top/index.xml" htmlUrl="https://time.com"/>
            <outline type="rss" text="Leaders" title="Leaders" xmlUrl="http://www.economist.com/rss/leaders_rss.xml" htmlUrl="https://www.economist.com/leaders"/>
            <outline type="rss" text="中国数字时代" title="中国数字时代" xmlUrl="http://feeds.feedburner.com/chinadigitaltimes/IyPt" htmlUrl="https://chinadigitaltimes.net/chinese"/>
            <outline type="rss" text="华尔街日报" title="华尔街日报" xmlUrl="https://cn.wsj.com/zh-hans/rss" htmlUrl="https://cn.wsj.com"/>
            <outline type="rss" text="BBC News 中文 (uploads) on YouTube" title="BBC News 中文 (uploads) on YouTube" xmlUrl="http://gdata.youtube.com/feeds/base/users/BBCZhongwen/uploads?alt=rss&amp;v=2&amp;orderby=published&amp;client=ytapi-youtube-profile" htmlUrl="https://www.youtube.com/playlist?list=UUb3TZ4SD_Ys3j4z0-8o6auA"/>
            <outline type="rss" text="What's on Weibo" title="What's on Weibo" xmlUrl="http://www.whatsonweibo.com/feed/" htmlUrl="https://www.whatsonweibo.com/"/>
            <outline type="rss" text="China Policy Institute Blog" title="China Policy Institute Blog" xmlUrl="http://blogs.nottingham.ac.uk/chinapolicyinstitute/feed/" htmlUrl="https://blogs.nottingham.ac.uk/chinapolicyinstitute"/>
        </outline>
        <outline text="骇客" title="骇客">
            <outline type="rss" text="Ideas Latest" title="Ideas Latest" xmlUrl="https://www.wired.com/feed/category/ideas/latest/rss" htmlUrl="https://www.wired.com"/>
            <outline type="rss" text="Culture Latest" title="Culture Latest" xmlUrl="http://blog.wired.com/underwire/atom.xml" htmlUrl="https://www.wired.com"/>
            <outline type="rss" text="Danylo Yakymenko" title="Danylo Yakymenko" xmlUrl="https://dandanua.github.io/feed.xml" htmlUrl="https://dandanua.github.io/"/>
            <outline type="rss" text="Science Latest" title="Science Latest" xmlUrl="http://blog.wired.com/wiredscience/rss.xml" htmlUrl="https://www.wired.com"/>
            <outline type="rss" text="Hacker News Best" title="Hacker News Best" xmlUrl="http://hnbest.heroku.com/rss" htmlUrl="https://news.ycombinator.com/best"/>
            <outline type="rss" text="Noticias de la Provincia de Santa Fe" title="Noticias de la Provincia de Santa Fe" xmlUrl="http://www.santafe.gov.ar/rss_noticias.php" htmlUrl="http://www.santafe.gov.ar/index.php"/>
            <outline type="rss" text="HackerNews" title="HackerNews" xmlUrl="http://hackernews.cc/feed" htmlUrl="https://hackernews.cc"/>
            <outline type="rss" text="Hacker News: Newest" title="Hacker News: Newest" xmlUrl="http://hnrss.org/newest?points=100" htmlUrl="https://news.ycombinator.com/newest"/>
            <outline type="rss" text="Fast Company - technology" title="Fast Company - technology" xmlUrl="https://www.fastcompany.com/technology/rss" htmlUrl="https://www.fastcompany.com"/>
            <outline type="rss" text="Backchannel Latest" title="Backchannel Latest" xmlUrl="https://www.wired.com/feed/category/backchannel/latest/rss" htmlUrl="https://www.wired.com"/>
            <outline type="rss" text="Hacker News" title="Hacker News" xmlUrl="http://www.reddit.com/r/hackernews/.rss" htmlUrl="https://www.reddit.com/r/hackernews/"/>
            <outline type="rss" text="BuzzFeed News" title="BuzzFeed News" xmlUrl="http://www.buzzfeed.com/longform.xml" htmlUrl="https://www.buzzfeednews.com"/>
            <outline type="rss" text="Real Python" title="Real Python" xmlUrl="https://realpython.com/atom.xml" htmlUrl="https://realpython.com/"/>
            <outline type="rss" text="Gear Latest" title="Gear Latest" xmlUrl="http://www.wired.com/reviews/feeds/latestProductsRss" htmlUrl="https://www.wired.com"/>
        </outline>
        <outline text="Must Read" title="Must Read">
            <outline type="rss" text="報導者" title="報導者" xmlUrl="https://www.twreporter.org/a/rss2.xml" htmlUrl="https://www.twreporter.org/"/>
            <outline type="rss" text="The Economist" title="The Economist" xmlUrl="http://www.economist.com/rss/the_world_this_week_rss.xml" htmlUrl="https://www.economist.com/the-world-this-week"/>
            <outline type="rss" text="Gartner Blog Network" title="Gartner Blog Network" xmlUrl="http://blogs.gartner.com/gbn/feed/" htmlUrl="https://www.gartner.com/en/blogs"/>
            <outline type="rss" text="The New Yorker - Culture" title="The New Yorker - Culture" xmlUrl="http://www.newyorker.com/services/rss/feeds/everything.xml" htmlUrl="https://www.newyorker.com/culture"/>
            <outline type="rss" text="36氪" title="36氪" xmlUrl="https://36kr.com/feed" htmlUrl="http://36kr.com"/>
            <outline type="rss" text="Hacker News" title="Hacker News" xmlUrl="http://www.reddit.com/r/hackernews/.rss" htmlUrl="https://www.reddit.com/r/hackernews/"/>
            <outline type="rss" text="Science Huβ Publishing- Leading the Information Highway" title="Science Huβ Publishing- Leading the Information Highway" xmlUrl="https://scihub.org/feed/" htmlUrl="https://www.scihub.org"/>
            <outline type="rss" text="Hacker News Best" title="Hacker News Best" xmlUrl="http://hnbest.heroku.com/rss" htmlUrl="https://news.ycombinator.com/best"/>
            <outline type="rss" text="Noticias de la Provincia de Santa Fe" title="Noticias de la Provincia de Santa Fe" xmlUrl="http://www.santafe.gov.ar/rss_noticias.php" htmlUrl="http://www.santafe.gov.ar/index.php"/>
            <outline type="rss" text="RSSHub 有新路由啦" title="RSSHub 有新路由啦" xmlUrl="https://rsshub.app/rsshub/rss" htmlUrl="https://docs.rsshub.app"/>
            <outline type="rss" text="阮一峰的网络日志" title="阮一峰的网络日志" xmlUrl="http://feeds.feedburner.com/ruanyifeng" htmlUrl="http://www.ruanyifeng.com/blog/"/>
            <outline type="rss" text="HackerNews" title="HackerNews" xmlUrl="http://hackernews.cc/feed" htmlUrl="https://hackernews.cc"/>
        </outline>
        <outline text="生活" title="生活">
            <outline type="rss" text="the Beijinger Blog" title="the Beijinger Blog" xmlUrl="http://www.thebeijinger.com/blog/feed" htmlUrl="https://www.thebeijinger.com/blog"/>
        </outline>
        <outline text="L" title="L">
            <outline type="rss" text="" title="" xmlUrl="https://rsshub.app/weibo/user/5442439205" htmlUrl="http://weibo.com/5442439205/"/>
        </outline>
        <outline text="数据" title="数据">
            <outline type="rss" text="Features – FiveThirtyEight" title="Features – FiveThirtyEight" xmlUrl="https://fivethirtyeight.com/features/feed/" htmlUrl="https://fivethirtyeight.com"/>
            <outline type="rss" text="visualcomplexity.com" title="visualcomplexity.com" xmlUrl="http://feeds.feedburner.com/visualcomplexity" htmlUrl="http://www.visualcomplexity.com"/>
            <outline type="rss" text="TRAVELGEAR- ĐÈN PIN" title="TRAVELGEAR- ĐÈN PIN" xmlUrl="http://feeds.feedburner.com/VizOfTheDay" htmlUrl="https://travelgear.vn/den-pin/"/>
        </outline>
        <outline text="艺术" title="艺术">
            <outline type="rss" text="The Paris Review" title="The Paris Review" xmlUrl="http://www.theparisreview.org/blog/feed/" htmlUrl="https://www.theparisreview.org/blog/"/>
            <outline type="rss" text="The Art Newspaper - International art news and events" title="The Art Newspaper - International art news and events" xmlUrl="http://theartnewspaper.com//rss.xml" htmlUrl="https://www.theartnewspaper.com"/>
        </outline>
        <outline text="文化" title="文化">
            <outline type="rss" text="The New Yorker - Culture" title="The New Yorker - Culture" xmlUrl="http://www.newyorker.com/services/rss/feeds/everything.xml" htmlUrl="https://www.newyorker.com/culture"/>
            <outline type="rss" text="Culture: TV, Movies, Music, Art, and Theatre News and Reviews" title="Culture: TV, Movies, Music, Art, and Theatre News and Reviews" xmlUrl="http://www.newyorker.com/feed/culture" htmlUrl="https://www.newyorker.com"/>
        </outline>
        <outline text="未来学" title="未来学">
            <outline type="rss" text="集智俱乐部" title="集智俱乐部" xmlUrl="http://swarma.org/feed/" htmlUrl="http://swarma.org"/>
            <outline type="rss" text="Gartner Blog Network" title="Gartner Blog Network" xmlUrl="http://blogs.gartner.com/gbn/feed/" htmlUrl="https://www.gartner.com/en/blogs"/>
            <outline type="rss" text="The Long Now Blog" title="The Long Now Blog" xmlUrl="http://feeds.feedburner.com/longnow" htmlUrl="https://longnow.org/"/>
            <outline type="rss" text="Falling Walls Fragments" title="Falling Walls Fragments" xmlUrl="https://www.fallingwallsfragments.com/feed/" htmlUrl="https://www.fallingwallsfragments.com"/>
        </outline>
        <outline type="rss" text="Science Huβ Publishing- Leading the Information Highway" title="Science Huβ Publishing- Leading the Information Highway" xmlUrl="https://scihub.org/feed/" htmlUrl="https://www.scihub.org"/>
        <outline type="rss" text="RSSHub 有新路由啦" title="RSSHub 有新路由啦" xmlUrl="https://rsshub.app/rsshub/rss" htmlUrl="https://docs.rsshub.app"/>
        <outline type="rss" text="阮一峰的网络日志" title="阮一峰的网络日志" xmlUrl="http://feeds.feedburner.com/ruanyifeng" htmlUrl="http://www.ruanyifeng.com/blog/"/>
    </body>
</opml>
