<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Transformation Progress Report</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            /* Set the exact canvas size */
            width: 2667px;
            height: 1500px;
            background-color: #FFFFFF; /* White background */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center; /* 改为居中对齐 */
            padding: 2rem; /* 减少整体padding */
            box-sizing: border-box; /* Include padding in the element's total width and height */
            overflow: hidden; /* Prevent scrollbars if content slightly exceeds bounds */
        }

        /* Custom scrollbar for better aesthetics, if any overflow happens */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Styles for content sections */
        .progress-section {
            background-color: #F2F2F2; /* Subtle #F2F2F2 section background */
            border-radius: 1.5rem; /* Rounded corners */
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05); /* Subtle shadow */
            padding: 3rem; /* Generous padding */
            margin-bottom: 2.5rem; /* Margin between sections */
            border: 1px solid rgba(0, 0, 0, 0.03); /* Very subtle border */
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            color: #242326; /* Default text color for content */
        }

        .progress-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
        }

        .section-title {
            font-size: 2.5rem; /* Larger font for section titles */
            font-weight: 700;
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #E0E0E0;
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
            font-size: 1.25rem; /* Larger font for tasks */
            line-height: 1.5;
        }

        .completed-task {
            color: #AE2105; /* Brand red for completed items */
            font-weight: 600;
        }

        .task-bullet {
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            background-color: #242326; /* Default bullet color */
            margin-right: 1rem;
            flex-shrink: 0;
            margin-top: 0.35rem; /* Align with text */
        }

        .completed-bullet {
            background-color: #AE2105; /* Red for completed bullets */
        }

        .progress-bar-container {
            width: 100%;
            background-color: #E0E0E0; /* Light grey track */
            border-radius: 0.75rem;
            height: 1.5rem; /* Thicker progress bar */
            overflow: hidden;
            margin-top: 1.5rem;
            position: relative;
        }

        .progress-bar-fill {
            height: 100%;
            background-color: #AE2105; /* Brand red fill */
            border-radius: 0.75rem;
            transition: width 0.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 1rem;
            box-sizing: border-box;
        }

        .progress-percentage {
            color: #FFFFFF;
            font-weight: 700;
            font-size: 1rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body class="text-gray-800">
    <!-- Title Section -->
    <div class="mb-16 text-center" style="margin-top: 6rem; margin-bottom: 4rem;">
        <h1 class="text-7xl font-bold" style="color: #AE2105; line-height: 1.2;">数字化工作上半年进展</h1>
        <p class="text-3xl text-gray-600 mt-4">Digital Transformation Progress Report (H1)</p>
    </div>

    <!-- Main Content Area - Progress Sections -->
    <div class="w-full max-w-8xl grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
        <!-- Work Stream 1: Core System Modernization -->
        <div class="progress-section">
            <h2 class="section-title">核心系统现代化</h2>
            <ul class="list-none p-0">
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成新系统需求分析与方案选定</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成供应商合同签署与团队组建</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>启动系统集成与数据迁移规划</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>完成核心模块开发 (20%)</span>
                </li>
            </ul>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 75%;">
                    <span class="progress-percentage">75%</span>
                </div>
            </div>
        </div>

        <!-- Work Stream 2: Data Foundation & Intelligence -->
        <div class="progress-section">
            <h2 class="section-title">数据基础与智能</h2>
            <ul class="list-none p-0">
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成数据平台架构设计</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成关键数据源集成 (80%)</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>启动数据治理框架建设</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>完成首批BI报表开发</span>
                </li>
            </ul>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 60%;">
                    <span class="progress-percentage">60%</span>
                </div>
            </div>
        </div>

        <!-- Work Stream 3: Digital Talent & Culture -->
        <div class="progress-section">
            <h2 class="section-title">数字化人才与文化</h2>
            <ul class="list-none p-0">
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成数字化能力评估与差距分析</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成首期内部培训课程开发与交付</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>启动跨部门数字化创新工作坊</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>制定年度数字化文化推广计划</span>
                </li>
            </ul>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 80%;">
                    <span class="progress-percentage">80%</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
