<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Content Slide</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /*
         * Global styles for the body and HTML to ensure full coverage and proper font.
         */
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            font-family: 'Inter', sans-serif; /* Using Inter font */
            overflow: hidden; /* Prevent scrolling */
        }

        /*
         * Container for the entire slide, setting the dimensions and background color.
         */
        .slide-container {
            width: 2667px; /* Canvas width */
            height: 1500px; /* Canvas height */
            background-color: #F2F2F2; /* Light gray background */
            color: #242326; /* Default text color for body */
            display: flex;
            box-sizing: border-box; /* Include padding in the element's total width and height */
            padding: 190px 540px 250px 200px; /* Top, Right, Bottom, Left margins */
            justify-content: flex-start; /* Align content to the start (left) */
            align-items: flex-start; /* Align content to the top */
        }

        /*
         * Content area within the slide, defining the layout for the title, text, and graphics.
         * It uses flexbox for vertical arrangement and ensures content is left-aligned.
         */
        .content-area {
            display: flex;
            flex-direction: column; /* Stack elements vertically */
            width: 100%; /* Take full width of the padded container */
            height: 100%; /* Take full height of the padded container */
            text-align: left; /* Ensure text alignment is left */
        }

        /*
         * Styling for the main title of the slide.
         * Uses primary red color and bold typography.
         */
        .slide-title {
            font-size: 90px; /* Large font size for headings */
            font-weight: 700; /* Bold typography */
            color: #AE2105; /* Primary red for headings */
            margin-bottom: 60px; /* Space below the title */
            line-height: 1.2;
        }

        /*
         * Styling for bullet points and general body text.
         * Uses the specified text color and a readable font size.
         */
        .bullet-points {
            list-style: none; /* Remove default list style */
            padding: 0;
            margin: 0;
            font-size: 50px; /* Readable font size for body text */
            font-weight: 400; /* Regular weight for body text */
            color: #242326; /* Dark text color */
            line-height: 1.6; /* Spacing between lines for readability */
            margin-bottom: 80px; /* Space below bullet points */
        }

        .bullet-points li {
            margin-bottom: 25px; /* Space between list items */
            position: relative;
            padding-left: 40px; /* Space for custom bullet */
        }

        .bullet-points li::before {
            content: '•'; /* Custom bullet point */
            color: #AE2105; /* Primary red for bullet */
            font-size: 1.2em; /* Slightly larger bullet */
            position: absolute;
            left: 0;
            top: 0;
        }

        /*
         * Container for graphics and data visualization.
         * Uses flexbox to arrange elements horizontally if needed.
         */
        .graphics-area {
            display: flex;
            justify-content: space-around; /* Distribute items evenly */
            align-items: center;
            width: 100%;
            flex-grow: 1; /* Allow it to take available space */
        }

        /*
         * Styling for a simple bar chart example.
         * Uses SVG for scalable graphics and brand colors.
         */
        .bar-chart-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 40px;
        }

        .bar-chart-container svg {
            width: 600px; /* Adjust size as needed */
            height: 400px; /* Adjust size as needed */
            background-color: #FFFFFF; /* White background for the chart area */
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .bar-chart-container text {
            font-size: 30px;
            fill: #242326;
            font-weight: 400;
        }

        /*
         * Responsive adjustments for smaller screens, ensuring the slide scales down gracefully.
         */
        @media (max-width: 2667px) {
            .slide-container {
                width: 100vw; /* Use viewport width */
                height: 100vh; /* Use viewport height */
                padding: 7vw 20vw 9vw 7.5vw; /* Scale padding proportionally */
            }
            .slide-title {
                font-size: 3.5vw; /* Scale font size proportionally */
                margin-bottom: 2.5vw;
            }
            .bullet-points {
                font-size: 2vw; /* Scale font size proportionally */
                margin-bottom: 3vw;
            }
            .bullet-points li {
                margin-bottom: 1vw;
                padding-left: 1.5vw;
            }
            .bullet-points li::before {
                font-size: 1.2em;
            }
            .bar-chart-container svg {
                width: 25vw;
                height: 15vw;
            }
            .bar-chart-container text {
                font-size: 1.2vw;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-area">
            <h1 class="slide-title">Key Insights and Strategic Focus</h1>

            <ul class="bullet-points">
                <li>[INSERT_CONTENT_HERE] - Comprehensive market analysis reveals significant growth opportunities in emerging sectors.</li>
                <li>[INSERT_CONTENT_HERE] - Our innovative product roadmap is aligned with customer needs and technological advancements.</li>
                <li>[INSERT_CONTENT_HERE] - Strategic partnerships are crucial for expanding our reach and enhancing service delivery.</li>
                <li>[INSERT_CONTENT_HERE] - Financial projections indicate strong profitability and sustainable long-term value creation.</li>
            </ul>

            <div class="graphics-area">
                <!-- Example Data Visualization: Simple Bar Chart using SVG -->
                <div class="bar-chart-container">
                    <svg viewBox="0 0 600 400">
                        <!-- Chart Title -->
                        <text x="300" y="50" text-anchor="middle" font-weight="bold">Quarterly Performance</text>

                        <!-- X-axis labels -->
                        <text x="150" y="380" text-anchor="middle">Q1</text>
                        <text x="300" y="380" text-anchor="middle">Q2</text>
                        <text x="450" y="380" text-anchor="middle">Q3</text>

                        <!-- Y-axis label -->
                        <text x="50" y="200" text-anchor="middle" transform="rotate(-90 50 200)">Revenue (M)</text>

                        <!-- Bars -->
                        <!-- Bar 1: Primary Red -->
                        <rect x="120" y="150" width="60" height="200" fill="#AE2105" rx="5" ry="5"></rect>
                        <text x="150" y="140" text-anchor="middle">$250</text>

                        <!-- Bar 2: Secondary Red (slightly different shade for contrast/highlight) -->
                        <rect x="270" y="100" width="60" height="250" fill="#C00000" rx="5" ry="5"></rect>
                        <text x="300" y="90" text-anchor="middle">$300</text>

                        <!-- Bar 3: Primary Red -->
                        <rect x="420" y="180" width="60" height="170" fill="#AE2105" rx="5" ry="5"></rect>
                        <text x="450" y="170" text-anchor="middle">$200</text>

                        <!-- Base line for bars -->
                        <line x1="100" y1="350" x2="500" y2="350" stroke="#242326" stroke-width="2"></line>
                        <line x1="100" y1="350" x2="100" y2="80" stroke="#242326" stroke-width="2"></line>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
