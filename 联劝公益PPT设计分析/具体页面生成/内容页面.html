<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional PowerPoint Slide</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom CSS for specific colors and fonts */
        :root {
            --primary-red: #AE2105;
            --secondary-red: #C00000;
            --text-color: #242326;
            --background-light-gray: #F2F2F2;
            --background-white: #FFFFFF;
        }

        body {
            font-family: 'Inter', sans-serif; /* Using Inter as specified default */
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh; /* Ensure it takes full viewport height for centering */
            background-color: #e0e0e0; /* A neutral background for the preview */
        }

        /* The main slide container, set to exact pixel dimensions */
        .slide-canvas {
            width: 2667px;
            height: 1500px;
            background-color: var(--background-light-gray); /* Or var(--background-white) */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Ensure content doesn't spill out */
            position: relative; /* For absolute positioning of elements if needed */
        }

        /* Content area with specified margins */
        .content-area {
            padding-left: 200px;
            padding-right: 540px;
            padding-top: 190px;
            padding-bottom: 250px;
            height: 100%; /* Take full height within the canvas */
            display: flex;
            flex-direction: column;
            justify-content: space-between; /* Distribute space between sections */
        }

        /* Typography styles */
        .heading {
            font-size: 72px; /* Large heading size */
            font-weight: 700; /* Bold */
            color: var(--primary-red);
            margin-bottom: 40px; /* Space below heading */
        }

        .bullet-point {
            font-size: 36px; /* Body text size */
            color: var(--text-color);
            margin-bottom: 20px; /* Space between bullet points */
            line-height: 1.4;
        }

        .bullet-point:last-child {
            margin-bottom: 0;
        }

        /* Data visualization container */
        .data-visualization {
            position: absolute;
            right: 200px; /* Aligned with the right margin */
            top: 300px; /* Adjust as needed */
            width: 400px; /* Example width */
            height: 600px; /* Example height */
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            padding: 20px;
            background-color: #ffffff; /* White background for the chart area */
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .bar-chart-container {
            display: flex;
            align-items: flex-end;
            height: 100%;
            width: 100%;
            gap: 15px; /* Space between bars */
            padding-bottom: 20px; /* Space for labels */
            border-bottom: 2px solid var(--text-color); /* X-axis line */
        }

        .bar {
            width: 60px; /* Width of each bar */
            border-radius: 5px 5px 0 0; /* Rounded top corners */
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
        }

        .bar-label {
            position: absolute;
            bottom: -30px; /* Position below the bar */
            font-size: 24px;
            color: var(--text-color);
        }

        .bar-value {
            font-size: 20px;
            color: white;
            margin-bottom: 10px;
            font-weight: bold;
        }

        /* Supporting graphic placeholder */
        .supporting-graphic {
            position: absolute;
            bottom: 100px; /* Adjust as needed */
            right: 200px; /* Aligned with the right margin */
            width: 400px;
            height: 250px;
            background-color: #E0E0E0; /* Light gray placeholder */
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28px;
            color: #888;
            border: 2px dashed #CCC;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
    </style>
</head>
<body>
    <div class="slide-canvas">
        <div class="content-area">
            <div>
                <h1 class="heading">Key Initiatives & Performance Overview</h1>
                <ul>
                    <li class="bullet-point">Strategic Growth: Achieved a 15% increase in market share through targeted campaigns and product innovation.</li>
                    <li class="bullet-point">Operational Efficiency: Streamlined processes led to a 10% reduction in overhead costs year-over-year.</li>
                    <li class="bullet-point">Customer Satisfaction: Improved NPS score by 8 points, reflecting enhanced service delivery and support.</li>
                    <li class="bullet-point">Talent Development: Implemented new training programs, resulting in a 20% increase in employee retention.</li>
                </ul>
            </div>
        </div>

        <!-- Data Visualization Element (Example Bar Chart) -->
        <div class="data-visualization">
            <h3 style="font-size: 32px; font-weight: bold; color: var(--text-color); margin-bottom: 20px;">Quarterly Performance</h3>
            <div class="bar-chart-container">
                <div class="bar" style="height: 80%; background-color: var(--primary-red);">
                    <span class="bar-value">Q1</span>
                    <span class="bar-label">80%</span>
                </div>
                <div class="bar" style="height: 65%; background-color: var(--secondary-red);">
                    <span class="bar-value">Q2</span>
                    <span class="bar-label">65%</span>
                </div>
                <div class="bar" style="height: 90%; background-color: var(--primary-red);">
                    <span class="bar-value">Q3</span>
                    <span class="bar-label">90%</span>
                </div>
                <div class="bar" style="height: 75%; background-color: var(--secondary-red);">
                    <span class="bar-value">Q4</span>
                    <span class="bar-label">75%</span>
                </div>
            </div>
            <p style="font-size: 20px; color: #666; margin-top: 20px;">*Data represents percentage of target achieved.</p>
        </div>

        <!-- Supporting Graphic Placeholder -->
        <div class="supporting-graphic">
            Supporting Graphic Placeholder
        </div>
    </div>
</body>
</html>
