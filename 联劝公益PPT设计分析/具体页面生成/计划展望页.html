<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Transformation Roadmap</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            /* Set the exact canvas size */
            width: 2667px;
            height: 1500px;
            /* Light gradient background from white to #F2F2F2 */
            background: linear-gradient(to bottom, #FFFFFF, #F2F2F2);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 4rem; /* Add some padding around the content */
            box-sizing: border-box; /* Include padding in the element's total width and height */
            overflow: hidden; /* Prevent scrollbars if content slightly exceeds bounds */
        }

        /* Custom scrollbar for better aesthetics, if any overflow happens */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Custom styles for timeline elements */
        .timeline-item {
            background-color: #ffffff;
            border-radius: 1.5rem; /* More rounded corners */
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08); /* Stronger shadow */
            padding: 2.5rem; /* More padding */
            margin-bottom: 2.5rem; /* More margin between items */
            border: 1px solid rgba(0, 0, 0, 0.05); /* Subtle border */
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .timeline-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
        }

        .priority-task {
            color: #AE2105;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .supporting-task {
            color: #A1D5FA;
            font-weight: 500;
            margin-left: 1.5rem; /* Indent supporting tasks */
            margin-bottom: 0.3rem;
            display: flex;
            align-items: center;
        }

        .priority-dot {
            width: 10px;
            height: 10px;
            background-color: #AE2105;
            border-radius: 50%;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        .supporting-dot {
            width: 8px;
            height: 8px;
            background-color: #A1D5FA;
            border-radius: 50%;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        .section-title {
            font-size: 2.25rem; /* Larger font for section titles */
            font-weight: 700;
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #E0E0E0;
        }

        .deliverable-tag {
            background-color: #E0F2F7; /* Light blue tag */
            color: #2A648C; /* Darker blue text */
            padding: 0.4rem 0.8rem;
            border-radius: 0.75rem;
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 1rem;
            display: inline-block;
        }

        .date-tag {
            background-color: #FEEBCB; /* Light orange tag */
            color: #C05621; /* Darker orange text */
            padding: 0.4rem 0.8rem;
            border-radius: 0.75rem;
            font-size: 0.9rem;
            font-weight: 600;
            margin-right: 1rem;
            display: inline-block;
        }
    </style>
</head>
<body class="text-gray-800">
    <!-- Title Section -->
    <div class="mb-12 text-center">
        <h1 class="text-7xl font-bold" style="color: #AE2105; line-height: 1.2;">下半年工作计划</h1>
        <p class="text-3xl text-gray-600 mt-4">Digital Transformation Roadmap</p>
    </div>

    <!-- Main Content Area - Timeline/Roadmap -->
    <div class="w-full max-w-7xl grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
        <!-- Quarter 3 - July-September -->
        <div class="timeline-item">
            <h2 class="section-title">第三季度 (7月-9月)</h2>
            <div class="mb-6">
                <span class="date-tag">目标日期: 9月30日</span>
                <p class="text-gray-600 text-lg mt-2">核心系统现代化与数据基础建设</p>
            </div>
            <ul>
                <li class="priority-task"><span class="priority-dot"></span>核心业务系统升级</li>
                <li class="supporting-task"><span class="supporting-dot"></span>需求分析与供应商评估</li>
                <li class="supporting-task"><span class="supporting-dot"></span>POC测试与方案选定</li>
                <li class="priority-task"><span class="priority-dot"></span>构建统一数据平台</li>
                <li class="supporting-task"><span class="supporting-dot"></span>数据源集成与ETL流程开发</li>
                <li class="supporting-task"><span class="supporting-dot"></span>数据湖/数据仓库搭建</li>
                <li class="priority-task"><span class="priority-dot"></span>数字化人才培训计划</li>
                <li class="supporting-task"><span class="supporting-dot"></span>内部培训课程开发</li>
                <li class="supporting-task"><span class="supporting-dot"></span>外部专家引进</li>
            </ul>
            <span class="deliverable-tag">关键交付: 升级方案报告, 数据平台原型</span>
        </div>

        <!-- Quarter 4 - October-December -->
        <div class="timeline-item">
            <h2 class="section-title">第四季度 (10月-12月)</h2>
            <div class="mb-6">
                <span class="date-tag">目标日期: 12月31日</span>
                <p class="text-gray-600 text-lg mt-2">客户体验优化与智能应用部署</p>
            </div>
            <ul>
                <li class="priority-task"><span class="priority-dot"></span>客户旅程数字化改造</li>
                <li class="supporting-task"><span class="supporting-dot"></span>线上服务流程优化</li>
                <li class="supporting-task"><span class="supporting-dot"></span>多渠道客户触点整合</li>
                <li class="priority-task"><span class="priority-dot"></span>AI/ML应用试点</li>
                <li class="supporting-task"><span class="supporting-dot"></span>智能客服机器人上线</li>
                <li class="supporting-task"><span class="supporting-dot"></span>个性化推荐系统开发</li>
                <li class="priority-task"><span class="priority-dot"></span>数据治理与安全体系建设</li>
                <li class="supporting-task"><span class="supporting-dot"></span>数据标准与规范制定</li>
                <li class="supporting-task"><span class="supporting-dot"></span>信息安全风险评估</li>
            </ul>
            <span class="deliverable-tag">关键交付: 客户体验报告, AI应用原型</span>
        </div>

        <!-- Future Initiatives / Next Steps -->
        <div class="timeline-item">
            <h2 class="section-title">未来展望与持续优化</h2>
            <div class="mb-6">
                <span class="date-tag">持续进行</span>
                <p class="text-gray-600 text-lg mt-2">创新孵化与生态系统构建</p>
            </div>
            <ul>
                <li class="priority-task"><span class="priority-dot"></span>建立创新实验室</li>
                <li class="supporting-task"><span class="supporting-dot"></span>探索前沿技术应用</li>
                <li class="supporting-task"><span class="supporting-dot"></span>孵化内部创新项目</li>
                <li class="priority-task"><span class="priority-dot"></span>深化合作伙伴关系</li>
                <li class="supporting-task"><span class="supporting-dot"></span>拓展技术与业务合作</li>
                <li class="supporting-task"><span class="supporting-dot"></span>共建数字化生态圈</li>
                <li class="priority-task"><span class="priority-dot"></span>持续绩效评估与迭代</li>
                <li class="supporting-task"><span class="supporting-dot"></span>定期回顾与调整策略</li>
                <li class="supporting-task"><span class="supporting-dot"></span>推广最佳实践</li>
            </ul>
            <span class="deliverable-tag">关键交付: 创新项目管道, 生态合作协议</span>
        </div>
    </div>
</body>
</html>
